import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import '../../../data/models/organisation_model.dart';
import '../services/organization_settings_service.dart';
import '../models/get_organization_response_model.dart';

class OrganizationSettingsController extends GetxController {
  final OrganizationSettingsService _organizationSettingsService = OrganizationSettingsService();
  final Logger logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxBool isUpdating = false.obs;
  String errorMessage = '';
  final Rx<Organisation?> organization = Rx<Organisation?>(null);
  final RxList<Account> accounts = <Account>[].obs;

  // Form controllers
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController businessNumberController = TextEditingController();
  final TextEditingController kraPinController = TextEditingController();
  final TextEditingController sloganController = TextEditingController();
  final TextEditingController usernameController = TextEditingController();

  // Form validation
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();
    fetchOrganization();
  }

  @override
  void onClose() {
    // Dispose controllers
    nameController.dispose();
    descriptionController.dispose();
    emailController.dispose();
    phoneController.dispose();
    businessNumberController.dispose();
    kraPinController.dispose();
    sloganController.dispose();
    usernameController.dispose();
    super.onClose();
  }

  /// Fetch organization details
  Future<void> fetchOrganization() async {
    try {
      isLoading.value = true;
      errorMessage = '';

      final response = await _organizationSettingsService.getOrganization();
      
      if (response['status'] == true && response['data'] != null) {
        final organisationResponse = OrganisationResponse.fromJson(response);
        organization.value = organisationResponse.data;
        
        if (organisationResponse.data?.accounts != null) {
          accounts.value = organisationResponse.data!.accounts!;
        }
        
        // Populate form controllers
        _populateFormControllers();
        
        logger.i('Organization details loaded successfully');
      } else {
        errorMessage = response['message'] ?? 'Failed to fetch organization details';
        ToastUtils.showErrorToast(
          'error', errorMessage,
          
        );
      }
    } catch (e) {
      logger.e('Error fetching organization: $e');
      errorMessage = 'An error occurred while fetching organization details';
      ToastUtils.showErrorToast(
        'error', errorMessage,
        
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Update organization details
  Future<void> updateOrganization() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      isUpdating.value = true;
      errorMessage = '';

      final organizationData = {
        'name': nameController.text.trim(),
        'description': descriptionController.text.trim(),
        'email': emailController.text.trim(),
        'phone_number': phoneController.text.trim(),
        'business_number': businessNumberController.text.trim(),
        'kra_pin': kraPinController.text.trim(),
        'slogan': sloganController.text.trim(),
        'username': usernameController.text.trim(),
      };

      final response = await _organizationSettingsService.updateOrganization(organizationData);
      
      if (response['status'] == true) {
        ToastUtils.showSuccessToast(
          'Success', 'Organization details updated successfully',
          
        );
        
        // Refresh organization data
        await fetchOrganization();
      } else {
        errorMessage = response['message'] ?? 'Failed to update organization details';
        ToastUtils.showErrorToast(
          'error',  errorMessage,
          
        );
      }
    } catch (e) {
      logger.e('Error updating organization: $e');
      errorMessage = 'An error occurred while updating organization details';
      ToastUtils.showErrorToast(
        'error', errorMessage,
        
      );
    } finally {
      isUpdating.value = false;
    }
  }

  /// Populate form controllers with organization data
  void _populateFormControllers() {
    if (organization.value != null) {
      final org = organization.value!;
      nameController.text = org.name ?? '';
      descriptionController.text = org.description ?? '';
      emailController.text = org.email ?? '';
      phoneController.text = org.phoneNumber ?? '';
      businessNumberController.text = org.businessNumber ?? '';
      kraPinController.text = org.kraPin ?? '';
      sloganController.text = org.slogan ?? '';
      usernameController.text = org.username ?? '';
    }
  }

  /// Refresh organization data
  Future<void> refreshOrganization() async {
    await fetchOrganization();
  }

  /// Get primary account
  Account? get primaryAccount {
    return accounts.firstWhereOrNull((account) => account.isPrimary == true);
  }

  /// Get SMS balance from primary account
  int get smsBalance {
    final primary = primaryAccount;
    return primary?.smsUnitsBalance ?? 0;
  }

  /// Get SMS rate from primary account
  double get smsRate {
    final primary = primaryAccount;
    return primary?.smsRate ?? 0.0;
  }
}
