import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../controllers/organization_settings_controller.dart';

class OrganizationInfoCard extends StatelessWidget {
  const OrganizationInfoCard({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrganizationSettingsController>();

    return Obx(() {
      final organization = controller.organization.value;
      if (organization == null) return const SizedBox.shrink();

      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10.r,
              offset: Offset(0, 2.h),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Organization Logo
                Container(
                  width: 60.w,
                  height: 60.h,
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.r),
                    image:
                        organization.logo != null &&
                                organization.logo!.isNotEmpty
                            ? DecorationImage(
                              image: NetworkImage(organization.logo!),
                              fit: BoxFit.cover,
                            )
                            : null,
                  ),
                  child:
                      organization.logo == null || organization.logo!.isEmpty
                          ? Icon(Icons.home, color: Colors.blue, size: 30)
                          : null,
                ),

                SizedBox(width: 12.w),

                // Organization Name and Status
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        organization.name ?? 'Organization Name',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      SizedBox(height: 4.h),

                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              color: _getStatusColor(
                                organization.status,
                              ).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6.r),
                            ),
                            child: Text(
                              organization.status ?? 'Unknown',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: _getStatusColor(organization.status),
                              ),
                            ),
                          ),

                          SizedBox(width: 8.w),

                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              color: _getVerificationStatusColor(
                                organization.verificationStatus,
                              ).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6.r),
                            ),
                            child: Text(
                              organization.verificationStatus ?? 'Unknown',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: _getVerificationStatusColor(
                                  organization.verificationStatus,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // Organization Details
            if (organization.description != null &&
                organization.description!.isNotEmpty)
              Text(
                organization.description!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.black54,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

            if (organization.slogan != null &&
                organization.slogan!.isNotEmpty) ...[
              SizedBox(height: 8.h),
              Text(
                '"${organization.slogan}"',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                  fontStyle: FontStyle.italic,
                  height: 1.4,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],

            SizedBox(height: 16.h),

            // Quick Stats
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.message,
                    label: 'SMS Balance',
                    value: '${controller.smsBalance}',
                  ),
                ),

                SizedBox(width: 16.w),

                Expanded(
                  child: _buildStatItem(
                    icon: Icons.account_balance_wallet,
                    label: 'SMS Rate',
                    value: '\$${controller.smsRate.toStringAsFixed(2)}',
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.blue, size: 20),

          SizedBox(height: 4.h),

          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          Text(
            label,
            style: const TextStyle(fontSize: 12, color: Colors.black54),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toUpperCase()) {
      case 'ACTIVE':
        return Colors.green;
      case 'INACTIVE':
        return Colors.red;
      case 'SUSPENDED':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Color _getVerificationStatusColor(String? status) {
    switch (status?.toUpperCase()) {
      case 'VERIFIED':
        return Colors.green;
      case 'PENDING':
        return Colors.orange;
      case 'REJECTED':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
